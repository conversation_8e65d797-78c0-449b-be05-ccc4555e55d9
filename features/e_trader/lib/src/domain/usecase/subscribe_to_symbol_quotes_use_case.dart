import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/domain/repository/symbol_quote_repository.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:prelude/prelude.dart';

class SubscribeToSymbolQuotesUseCase {
  final SymbolQuoteRepository symbolQuoteRepository;
  final GetAccountNumberUseCase getAccountNumberUseCase;
  const SubscribeToSymbolQuotesUseCase({
    required this.getAccountNumberUseCase,
    required this.symbolQuoteRepository,
  });

  TaskEither<Exception, Stream<SymbolQuoteModel>> call({
    required String symbol,
    required String subscriberId,
  }) {
    return getAccountNumberUseCase().toTaskEither().flatMap((accountNumber) {
      return symbolQuoteRepository.subscribeToSymbolQuotes(
        symbol: symbol,
        accountNumber: accountNumber,
        subscriberId: subscriberId,
      );
    });
  }
}

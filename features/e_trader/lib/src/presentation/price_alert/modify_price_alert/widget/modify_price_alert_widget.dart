// ignore_for_file: prefer-number-format

import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/price_alert.dart';
import 'package:e_trader/src/domain/model/set_price_alert_model.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/buy_sell/buy_sell_buttons.dart';
import 'package:e_trader/src/presentation/model/buy_sell_button_state.dart';
import 'package:e_trader/src/presentation/price_alert/edit_price_alert/edit_price_alert_bloc.dart';
import 'package:e_trader/src/presentation/price_alert/edit_price_alert/widget/edit_price_alert_widget.dart';
import 'package:e_trader/src/presentation/price_alert/price_alert_list_item_widget.dart';
import 'package:e_trader/src/presentation/price_alert/set_price_alert/bloc/set_price_alert_bloc.dart';
import 'package:e_trader/src/presentation/trading_keyboard/trading_keyboard.dart';
import 'package:e_trader/src/presentation/trading_keyboard/trading_keyboard_input_control.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ModifyPriceAlertWidget extends StatefulWidget {
  final PriceAlert alert;
  final void Function(bool)? onHideTabbar;
  // final BuildContext parentContext;

  const ModifyPriceAlertWidget({
    super.key,
    required this.alert,
    this.onHideTabbar,
    // required this.parentContext,
  });

  @override
  State<ModifyPriceAlertWidget> createState() => _ModifyPriceAlertWidgetState();
}

class _ModifyPriceAlertWidgetState extends State<ModifyPriceAlertWidget>
    with AutomaticKeepAliveClientMixin {
  TradingKeyboardInputControl _inputControl = TradingKeyboardInputControl();

  final animationDuration = Duration(milliseconds: 250);
  double stepperControlDefaultPosition = 100 + 75 + 24;

  void _showSuccessToastMessage({
    required String title,
    required TradeType tradeType,
    required int digits,
    required double enteredPrice,
  }) {
    final toast = DuploToast();
    toast.showToastMessage(
      context: context,
      widget: DuploToastDecoratorWidget(
        messageType: ToastMessageType.success,
        statusColor:
            tradeType == TradeType.buy
                ? context.duploTheme.utility.utilitySuccess600
                : context.duploTheme.utility.utilityError600,
        contentWidget: PriceAlertListItemWidget(
          alertPrice: enteredPrice,
          symbolName: widget.alert.tickerName,
          productLogoUrl: widget.alert.productLogoUrl,
          digits: digits,
          tradeType: tradeType,
        ),
        titleMessage: title,
        onLeadingAction: () {
          toast.hidesToastMessage();
        },
        onTap: () {
          toast.hidesToastMessage();
        },
      ),
    );
  }

  void _showFailureToastMessage(String title, String description) {
    final toast = DuploToast();
    toast.showToastMessage(
      context: context,
      widget: DuploToastMessage(
        titleMessage: title,
        descriptionMessage: description,
        messageType: ToastMessageType.error,
        onLeadingAction: () {
          toast.hidesToastMessage();
        },
      ),
    );
  }

  void _deleteAlert(BuildContext builderContext, SetPriceAlertModel info) {
    if (info.viewState == SetPriceAlertViewState.idle) {
      builderContext.read<SetPriceAlertBloc>().add(
        SetPriceAlertEvent.onDeleteAlert(widget.alert),
      );
    }
  }

  void _modifyAlert(BuildContext builderContext, SetPriceAlertModel info) {
    if (info.viewState == SetPriceAlertViewState.idle) {
      builderContext.read<SetPriceAlertBloc>().add(
        SetPriceAlertEvent.onModifyAlert(widget.alert),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final localization = EquitiLocalization.of(context);

    return MultiBlocProvider(
      providers: [
        BlocProvider<SetPriceAlertBloc>(
          create:
              (_) =>
                  diContainer<SetPriceAlertBloc>()..add(
                    SetPriceAlertEvent.fetchSymbolDetails(
                      widget.alert.platformName,
                      widget.alert,
                    ),
                  ),
        ),
        BlocProvider<EditPriceAlertBloc>(
          create: (_) => diContainer<EditPriceAlertBloc>(),
        ),
      ],
      child: BlocConsumer<SetPriceAlertBloc, SetPriceAlertState>(
        listenWhen: (previous, current) {
          log("print ----- UI updates");
          return switch (previous) {
            SetPriceAlertSuccess(info: final previousInfo) => switch (current) {
              SetPriceAlertSuccess(info: final currentInfo) =>
                currentInfo.viewState != previousInfo.viewState,
              _ => false,
            },
            _ => false,
          };
        },
        listener: (listnerContext, state) {
          if (state case SetPriceAlertSuccess(:final info)) {
            bool removeCard = false;
            String? deletedAlertId;

            switch (info.viewState) {
              case SetPriceAlertViewState.deleteSuccess:
                _showSuccessToastMessage(
                  title: localization.trader_alertHasBeenDeleted,
                  tradeType: info.selctedTradeType,
                  enteredPrice: widget.alert.priceAlertPrice,
                  digits: info.prices.digits,
                );
                removeCard = true;
                deletedAlertId =
                    widget.alert.priceAlertId; // Capture deleted alert ID
                break;
              case SetPriceAlertViewState.modificationSuccess:
                _showSuccessToastMessage(
                  title: localization.trader_alertHasBeenModified,
                  tradeType: info.selctedTradeType,
                  enteredPrice: info.enteredPrice,
                  digits: info.prices.digits,
                );
                removeCard = true;
                break;
              case SetPriceAlertViewState.deleteFailed:
                _showFailureToastMessage(
                  localization.trader_alertNotDeleted,
                  localization.trader_unableToDeleteAlertMessage,
                );
                break;
              case SetPriceAlertViewState.modificationFailed:
                _showFailureToastMessage(
                  localization.trader_alertNotSaved,
                  localization.trader_unableToModifyAlertMessage,
                );
              default:
                break;
            }

            if (removeCard) {
              Navigator.of(
                listnerContext,
              ).maybePop(deletedAlertId); // Return the deleted alert ID
            }
          }
        },
        buildWhen: (previous, current) {
          final shouldBuild = current != previous;
          log(
            "print ----- buildWhen: shouldBuild=$shouldBuild, previous=${previous.runtimeType}, current=${current.runtimeType}",
          );
          if (previous is SetPriceAlertSuccess &&
              current is SetPriceAlertSuccess) {
            // ignore: prefer-number-format
            log(
              "print ----- buildWhen: previous.ask=${previous.info.prices.ask}, current.ask=${current.info.prices.ask}",
            );
          }
          return shouldBuild;
        },
        builder: (builderContext, state) {
          final totalWidth = MediaQuery.sizeOf(builderContext).width;
          final widthAvaliable =
              MediaQuery.sizeOf(builderContext).width - (16 * 2);

          return switch (state) {
            SetPriceAlertLoading() => Padding(
              padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
              child: Column(
                children: [
                  const DuploMultipleShimmersInRow(count: 2, height: 80),
                  SizedBox(height: 16),
                  const DuploMultipleShimmersInRow(count: 1, height: 140),
                ],
              ),
            ),
            SetPriceAlertError() => Center(
              child: EmptyOrErrorStateComponent.defaultError(
                builderContext,
                () {
                  builderContext.read<SetPriceAlertBloc>().add(
                    SetPriceAlertEvent.fetchSymbolDetails(
                      widget.alert.platformName,
                      widget.alert,
                    ),
                  );
                },
              ),
            ),
            SetPriceAlertSuccess(:final info) => () {
              final alertTradeType = widget.alert.alertTradeType;
              return ValueListenableBuilder(
                valueListenable: _inputControl.visible,
                builder: (buildContext, visible, __) {
                  return Container(
                    width: totalWidth,
                    child: Stack(
                      alignment: AlignmentDirectional.topCenter,
                      children: [
                        Positioned(
                          bottom: 0,
                          child: AnimatedOpacity(
                            duration: animationDuration,
                            opacity: visible ? 1 : 0,
                            child: Container(
                              width: widthAvaliable,
                              child: TradingKeyboard(
                                inputControl: _inputControl,
                                onDone: () {
                                  _inputControl.hide();
                                  FocusScope.of(context).unfocus();
                                },
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          top: 0,
                          child: AnimatedOpacity(
                            opacity: visible ? 0 : 1,
                            duration: animationDuration,
                            child: Container(
                              width: totalWidth,
                              child: PriceAlertListItemWidget(
                                alertPrice: widget.alert.priceAlertPrice,
                                currentPrice:
                                    alertTradeType == TradeType.buy
                                        ? info.prices.ask
                                        : info.prices.bid,
                                symbolName: widget.alert.tickerName,
                                productLogoUrl: widget.alert.productLogoUrl,
                                digits: info.prices.digits,
                                priceDirection: widget.alert.priceDirection,
                                tradeType: alertTradeType,
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          top: 100,
                          child: AnimatedOpacity(
                            duration: animationDuration,
                            opacity: visible ? 0 : 1,
                            child: Container(
                              width: widthAvaliable,
                              child: BuySellButtons(
                                digits: info.prices.digits,
                                //user should not be able to change the trade type in modify alert
                                // ignore: no-empty-block
                                onTap: (tradeType) {},
                                spread: info.prices.spread,
                                buyButtonState:
                                    info.selctedTradeType == TradeType.buy
                                        ? BuySellButtonState.selected(
                                          info.prices.ask,
                                        )
                                        : BuySellButtonState.active(
                                          info.prices.ask,
                                        ),
                                sellButtonState:
                                    info.selctedTradeType == TradeType.sell
                                        ? BuySellButtonState.selected(
                                          info.prices.bid,
                                        )
                                        : BuySellButtonState.active(
                                          info.prices.bid,
                                        ),
                              ),
                            ),
                          ),
                        ),
                        AnimatedPositioned(
                          duration: animationDuration,
                          top: visible ? 8 : stepperControlDefaultPosition,
                          child: Container(
                            width: widthAvaliable,
                            child: EditPriceAlertWidget(
                              inputControl: _inputControl,
                              enteredPrice: info.enteredPrice,
                              tradeType: info.selctedTradeType,
                              symbolPrice:
                                  info.selctedTradeType == TradeType.buy
                                      ? info.prices.ask
                                      : info.prices.bid,
                              digits: info.prices.digits,
                              onStateChanged: (editState) {
                                builderContext.read<SetPriceAlertBloc>().add(
                                  SetPriceAlertEvent.onEditPriceStateChanged(
                                    editState,
                                  ),
                                );
                              },
                              initLocale:
                                  Localizations.localeOf(
                                    buildContext,
                                  ).toString(),
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 0,
                          child: IgnorePointer(
                            ignoring: visible,
                            child: AnimatedOpacity(
                              duration: animationDuration,
                              opacity: visible ? 0 : 1,
                              child: Container(
                                width: widthAvaliable,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: DuploButton.secondary(
                                        title: localization.trader_delete,
                                        onTap: () {
                                          _deleteAlert(builderContext, info);
                                        },
                                        loadingText:
                                            localization.trader_deleting,
                                        isLoading:
                                            info.viewState ==
                                            SetPriceAlertViewState.deleting,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      flex: 2,
                                      child: DuploButton.defaultPrimary(
                                        title: localization.trader_saveAlert,
                                        isDisabled: info.vaidationError,
                                        onTap: () {
                                          _modifyAlert(builderContext, info);
                                        },
                                        loadingText: localization.trader_saving,
                                        isLoading:
                                            info.viewState ==
                                            SetPriceAlertViewState.modifying,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            }(),
          };
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

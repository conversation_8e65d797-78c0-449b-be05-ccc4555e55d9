import 'package:duplo/duplo.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as Trader;
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/price_alert.dart';
import 'package:e_trader/src/domain/usecase/update_active_alerts_hub_use_case.dart';
import 'package:e_trader/src/presentation/price_alert/active_price_alerts/bloc/active_price_alerts_bloc.dart';
import 'package:e_trader/src/presentation/price_alert/modify_price_alert/widget/modify_price_alert_widget.dart';
import 'package:e_trader/src/presentation/price_alert/price_alert_list_item_widget.dart';
import 'package:e_trader/src/presentation/symbols/widgets/tab_subscription_manager.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';

class ActivePriceAlertsWidget extends StatefulWidget {
  final String? platformName;
  final bool showEditableOptions;
  final TabController tabController;
  final int tabIndex;
  final TabSubscriptionManager tabSubscriptionManager;

  const ActivePriceAlertsWidget({
    super.key,
    this.platformName,
    this.showEditableOptions = false,
    required this.tabController,
    required this.tabIndex,
    required this.tabSubscriptionManager,
  });

  @override
  State<ActivePriceAlertsWidget> createState() =>
      _ActivePriceAlertsWidgetState();
}

class _ActivePriceAlertsWidgetState extends State<ActivePriceAlertsWidget>
    with RouteAwareAppLifecycleMixin, AutomaticKeepAliveClientMixin {
  bool _hasSubscribed = false;

  @override
  void initState() {
    super.initState();

    // Register with the centralized tab subscription manager
    // Use onFirstVisit for first-time subscription and onSubscribe for subsequent visits
    widget.tabSubscriptionManager.registerTabItem(
      tabIndex: widget.tabIndex,
      onSubscribe: _subscribe,
      onUnsubscribe: _unsubscribe,
      onFirstVisit:
          () => context.read<ActivePriceAlertsBloc>().add(
            ActivePriceAlertsEvent.startActiveAlerts(
              symbol: widget.platformName,
            ),
          ),
    );
  }

  @override
  void dispose() {
    // Unregister from the centralized tab subscription manager
    widget.tabSubscriptionManager.unregisterTabItem(
      tabIndex: widget.tabIndex,
      onSubscribe: _subscribe,
      onUnsubscribe: _unsubscribe,
      onFirstVisit:
          () => context.read<ActivePriceAlertsBloc>().add(
            ActivePriceAlertsEvent.startActiveAlerts(
              symbol: widget.platformName,
            ),
          ),
    );

    _unsubscribe();

    super.dispose();
  }

  void _showModifyPriceAlert(PriceAlert alert, BuildContext context) async {
    final deletedAlertId = await DuploSheet.showModalSheetV2<String?>(
      context,
      appBar: DuploAppBar(
        title: EquitiLocalization.of(context).trader_modifyAlert,
        automaticallyImplyLeading: false,
        duploAppBarTextAlign: DuploAppBarTextAlign.left,
        actions: [
          IconButton(
            icon: Assets.images.closeIc.svg(),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      bottomBar: Container(height: 0),
      content: ColoredBox(
        color: context.duploTheme.background.bgSecondary,
        child: Padding(
          padding: EdgeInsets.only(bottom: 16),
          child: ModifyPriceAlertWidget(alert: alert),
        ),
      ),
    );

    // If an alert was deleted, remove it from local state immediately
    if (deletedAlertId != null && mounted) {
      context.read<ActivePriceAlertsBloc>().add(
        ActivePriceAlertsEvent.removeDeletedAlert(deletedAlertId),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final localization = EquitiLocalization.of(context);

    return BlocConsumer<ActivePriceAlertsBloc, ActivePriceAlertsState>(
      listenWhen:
          (previous, current) =>
              previous.processState != current.processState &&
              current.processState is ActivePriceAlertsConnected,
      listener: (listenerContext, _) {
        if (widget.tabController.indexIsChanging) return;
        if (widget.tabController.index != widget.tabIndex) return;
        _subscribe();
      },
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        return CustomScrollView(
          slivers: [
            switch (state.processState) {
              ActivePriceAlertsLoading() ||
              ActivePriceAlertsConnected() => DuploShimmerList.sliver(
                hasLeading: true,
                hasTrailing: true,
                itemShimmerType: DuploShimmerType.static,
              ),
              ActivePriceAlertsSuccess() => () {
                if (state.alerts.isEmpty) {
                  return SliverFillRemaining(
                    hasScrollBody: false,
                    child: EmptyOrErrorStateComponent.empty(
                      description:
                          localization.trader_noActiveAlertsDescription,
                      title: localization.trader_noAlerts,
                      svgImage:
                          Trader.Assets.images.portfolioEmptyAlertList.svg(),
                      isCenterlized: true,
                    ),
                  );
                }

                return SliverPadding(
                  padding: const EdgeInsets.only(bottom: 25),
                  sliver: SliverList.builder(
                    itemCount: state.alerts.length,
                    itemBuilder: (sliverBuilderContext, index) {
                      final alertViewModel = state.alerts[index];
                      final alert = state.domainAlerts.firstOrNullWhere(
                        (domainAlert) =>
                            domainAlert.priceAlertId ==
                            alertViewModel.priceAlertId,
                      );
                      if (alert == null) {
                        return SizedBox.shrink();
                      }
                      return DuploTap(
                        onTap:
                            () => _showModifyPriceAlert(
                              alert,
                              sliverBuilderContext,
                            ),
                        child: PriceAlertListItemWidget(
                          distance: alert.distance,
                          alertPrice: alert.priceAlertPrice,
                          currentPrice: alert.currentPrice,
                          symbolName: alert.tickerName,
                          productLogoUrl: alert.productLogoUrl,
                          digits: alert.digits,
                          priceDirection: alert.priceDirection,
                          tradeType: alert.alertTradeType,
                        ),
                      );
                    },
                  ),
                );
              }(),
              ActivePriceAlertsEmpty() => SliverFillRemaining(
                hasScrollBody: false,
                child: EmptyOrErrorStateComponent.empty(
                  description: localization.trader_noActiveAlertsDescription,
                  title: localization.trader_noAlerts,
                  svgImage: Trader.Assets.images.portfolioEmptyAlertList.svg(),
                  isCenterlized: true,
                ),
              ),
              ActivePriceAlertsError() => SliverFillRemaining(
                hasScrollBody: false,
                child: EmptyOrErrorStateComponent.defaultError(
                  builderContext,
                  () => builderContext.read<ActivePriceAlertsBloc>().add(
                    ActivePriceAlertsEvent.startActiveAlerts(
                      symbol: widget.platformName,
                    ),
                  ),
                ),
              ),
            },
          ],
        );
      },
    );
  }

  @override
  void onAppForeground() {
    // App came to foreground - subscribe if this tab is active and route is visible
    if (!widget.tabSubscriptionManager.isActive) return;
    if (widget.tabController.indexIsChanging) return;
    if (widget.tabController.index == widget.tabIndex) {
      _subscribe();
    }
  }

  @override
  void onAppBackground() {
    // App went to background - unsubscribe to save resources
    if (!widget.tabSubscriptionManager.isActive) return;
    if (widget.tabController.indexIsChanging) return;
    if (widget.tabController.index == widget.tabIndex) {
      _unsubscribe();
    }
  }

  @override
  void onRoutePopped(Route<Object?> route) {
    // Route visibility is automatically tracked by the mixin
    // Don't handle any route changes if parent tab subscription manager is paused or context is inactive
    if (!widget.tabSubscriptionManager.isActive) return;

    // Only handle route changes if this is the currently selected tab
    if (widget.tabController.indexIsChanging) return;
    if (widget.tabController.index != widget.tabIndex) return;
    _subscribe();
  }

  @override
  void onRoutePushed(Route<Object?> route) {
    // Route visibility is automatically tracked by the mixin
    // Don't handle any route changes if parent tab subscription manager is paused or context is inactive
    if (!widget.tabSubscriptionManager.isActive) return;

    // Only handle route changes if this is the currently selected tab
    if (widget.tabController.indexIsChanging) return;
    if (widget.tabController.index != widget.tabIndex) return;
    _unsubscribe();
  }

  void _subscribe() {
    if (!_hasSubscribed) {
      if (mounted)
        context.read<ActivePriceAlertsBloc>().add(
          ActivePriceAlertsEvent.updateActiveAlerts(
            TradingSocketEvent.priceAlert.subscribe,
            widget.platformName,
          ),
        );
      _hasSubscribed = true;
    }
  }

  void _unsubscribe() {
    if (_hasSubscribed) {
      try {
        diContainer<UpdateActiveAlertsHubUseCase>().call(
          eventType: TradingSocketEvent.priceAlert.unsubscribe,
          symbol: widget.platformName,
        );
      } catch (e) {
        print('Error unsubscribing from active alerts: $e');
      }

      _hasSubscribed = false;
    }
  }

  @override
  bool get wantKeepAlive => true;
}
